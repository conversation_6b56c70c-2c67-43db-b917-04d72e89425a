@echo off
echo Creating folder index...
echo ^<!DOCTYPE html^> > folder_index.html
echo ^<html^> >> folder_index.html
echo ^<head^> >> folder_index.html
echo ^<title^>Folder Index^</title^> >> folder_index.html
echo ^<style^> >> folder_index.html
echo body { font-family: Arial, sans-serif; margin: 20px; } >> folder_index.html
echo h1, h2 { color: #333; } >> folder_index.html
echo .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; } >> folder_index.html
echo pre { background-color: #f9f9f9; padding: 10px; border-radius: 5px; overflow-x: auto; } >> folder_index.html
echo ^</style^> >> folder_index.html
echo ^</head^> >> folder_index.html
echo ^<body^> >> folder_index.html
echo ^<h1^>Folder Index^</h1^> >> folder_index.html

echo ^<div class="stats"^> >> folder_index.html
echo ^<h2^>Statistics^</h2^> >> folder_index.html
echo ^<p^>Indexed at: %date% %time%^</p^> >> folder_index.html
echo ^</div^> >> folder_index.html

echo ^<h2^>Directory Structure^</h2^> >> folder_index.html
echo ^<pre^> >> folder_index.html

dir /s /b >> folder_index.html

echo ^</pre^> >> folder_index.html
echo ^</body^> >> folder_index.html
echo ^</html^> >> folder_index.html

echo Index created: folder_index.html
start folder_index.html
