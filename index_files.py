import os
import datetime
import json
import hashlib
import mimetypes
from pathlib import Path
import argparse

class FileIndexer:
    """A tool to create a comprehensive index of files in a directory."""
    
    def __init__(self, root_dir, output_file='file_index.html', include_content=False, max_content_size=10240):
        """
        Initialize the FileIndexer.
        
        Args:
            root_dir (str): The root directory to index
            output_file (str): The output HTML file path
            include_content (bool): Whether to include file content in the index
            max_content_size (int): Maximum file size to include content (in bytes)
        """
        self.root_dir = os.path.abspath(root_dir)
        self.output_file = output_file
        self.include_content = include_content
        self.max_content_size = max_content_size
        self.index = {
            'indexed_at': datetime.datetime.now().isoformat(),
            'root_directory': self.root_dir,
            'total_files': 0,
            'total_directories': 0,
            'total_size': 0,
            'files': [],
            'directories': []
        }
    
    def human_readable_size(self, size, decimal_places=2):
        """Convert bytes to human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB', 'PB']:
            if size < 1024.0 or unit == 'PB':
                break
            size /= 1024.0
        return f"{size:.{decimal_places}f} {unit}"
    
    def get_file_info(self, file_path):
        """Get metadata for a file."""
        try:
            stat = os.stat(file_path)
            file_size = stat.st_size
            
            # Get file type using mimetypes
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = "application/octet-stream"
                
            # Calculate MD5 hash for smaller files
            file_hash = None
            if file_size <= 1024 * 1024:  # Only hash files <= 1MB
                try:
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()
                except Exception:
                    pass
            
            # Get file content if requested and file is small enough
            content = None
            if self.include_content and file_size <= self.max_content_size:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                except Exception:
                    pass
            
            return {
                'name': os.path.basename(file_path),
                'path': os.path.relpath(file_path, self.root_dir),
                'size': file_size,
                'size_human': self.human_readable_size(file_size),
                'created': datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'mime_type': mime_type,
                'extension': os.path.splitext(file_path)[1].lower(),
                'md5': file_hash,
                'content': content
            }
        except Exception as e:
            return {
                'name': os.path.basename(file_path),
                'path': os.path.relpath(file_path, self.root_dir),
                'error': str(e)
            }
    
    def index_directory(self):
        """Index all files and directories in the root directory."""
        for root, dirs, files in os.walk(self.root_dir):
            # Add directory info
            rel_path = os.path.relpath(root, self.root_dir)
            if rel_path == '.':
                rel_path = ''
                
            self.index['directories'].append({
                'name': os.path.basename(root) or os.path.basename(self.root_dir),
                'path': rel_path,
                'file_count': len(files),
                'subdir_count': len(dirs)
            })
            self.index['total_directories'] += 1
            
            # Add file info
            for file in files:
                file_path = os.path.join(root, file)
                file_info = self.get_file_info(file_path)
                self.index['files'].append(file_info)
                self.index['total_files'] += 1
                
                # Add to total size if size is available
                if 'size' in file_info:
                    self.index['total_size'] += file_info['size']
    
    def generate_html(self):
        """Generate an HTML report from the index."""
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Index: {os.path.basename(self.root_dir)}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        h1, h2, h3 {{ color: #333; }}
        .stats {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .directory {{ margin-bottom: 30px; }}
        .file {{ margin-bottom: 15px; background-color: #f9f9f9; padding: 10px; border-radius: 5px; }}
        .file-header {{ font-weight: bold; margin-bottom: 5px; }}
        .file-meta {{ color: #666; font-size: 0.9em; }}
        .search-container {{ margin-bottom: 20px; }}
        #search {{ padding: 8px; width: 300px; }}
        .hidden {{ display: none; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f2f2f2; }}
        tr:hover {{ background-color: #f5f5f5; }}
    </style>
</head>
<body>
    <h1>File Index: {os.path.basename(self.root_dir)}</h1>
    
    <div class="search-container">
        <input type="text" id="search" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: {self.index['indexed_at']}</p>
        <p>Root Directory: {self.index['root_directory']}</p>
        <p>Total Files: {self.index['total_files']}</p>
        <p>Total Directories: {self.index['total_directories']}</p>
        <p>Total Size: {self.human_readable_size(self.index['total_size'])}</p>
    </div>
    
    <h2>Directory Structure</h2>
    <table>
        <tr>
            <th>Directory</th>
            <th>Path</th>
            <th>Files</th>
            <th>Subdirectories</th>
        </tr>
"""
        
        # Sort directories by path
        sorted_dirs = sorted(self.index['directories'], key=lambda x: x['path'] if x['path'] else "")
        
        for dir_info in sorted_dirs:
            dir_name = dir_info['name']
            dir_path = dir_info['path'] if dir_info['path'] else "Root"
            
            html += f"""
        <tr class="dir-row" data-path="{dir_path.lower()}">
            <td>{dir_name}</td>
            <td>{dir_path}</td>
            <td>{dir_info['file_count']}</td>
            <td>{dir_info['subdir_count']}</td>
        </tr>"""
        
        html += """
    </table>
    
    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Path</th>
            <th>Size</th>
            <th>Modified</th>
            <th>Type</th>
        </tr>
"""
        
        # Sort files by path
        sorted_files = sorted(self.index['files'], key=lambda x: x['path'])
        
        for file_info in sorted_files:
            file_name = file_info['name']
            file_path = file_info['path']
            file_size = file_info.get('size_human', 'Unknown')
            file_modified = file_info.get('modified', 'Unknown').split('T')[0]
            file_type = file_info.get('mime_type', 'Unknown')
            
            html += f"""
        <tr class="file-row" data-name="{file_name.lower()}" data-path="{file_path.lower()}">
            <td>{file_name}</td>
            <td>{file_path}</td>
            <td>{file_size}</td>
            <td>{file_modified}</td>
            <td>{file_type}</td>
        </tr>"""
        
        # Add search functionality
        html += """
    </table>
    
    <script>
        document.getElementById('search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            
            // Search in directories
            document.querySelectorAll('.dir-row').forEach(row => {
                const path = row.getAttribute('data-path');
                if (path.includes(searchTerm)) {
                    row.classList.remove('hidden');
                } else {
                    row.classList.add('hidden');
                }
            });
            
            // Search in files
            document.querySelectorAll('.file-row').forEach(row => {
                const name = row.getAttribute('data-name');
                const path = row.getAttribute('data-path');
                if (name.includes(searchTerm) || path.includes(searchTerm)) {
                    row.classList.remove('hidden');
                } else {
                    row.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
"""
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"HTML report generated: {self.output_file}")
    
    def run(self):
        """Run the indexing process."""
        print(f"Indexing directory: {self.root_dir}")
        self.index_directory()
        
        # Add human-readable total size
        self.index['total_size_human'] = self.human_readable_size(self.index['total_size'])
        
        # Generate HTML report
        self.generate_html()
        
        return self.index

def main():
    parser = argparse.ArgumentParser(description='Index files in a directory')
    parser.add_argument('--dir', '-d', required=True, help='Directory to index')
    parser.add_argument('--output', '-o', default='file_index.html', help='Output HTML file (default: file_index.html)')
    parser.add_argument('--include-content', '-c', action='store_true', help='Include file content in the index')
    parser.add_argument('--max-content-size', '-m', type=int, default=10240, help='Maximum file size to include content (default: 10KB)')
    
    args = parser.parse_args()
    
    indexer = FileIndexer(
        root_dir=args.dir,
        output_file=args.output,
        include_content=args.include_content,
        max_content_size=args.max_content_size
    )
    
    indexer.run()

if __name__ == "__main__":
    main()
