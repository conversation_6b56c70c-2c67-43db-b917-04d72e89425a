# PDF Data Enrichment Tool

This tool extracts and enriches information from PDF files using GPU acceleration.

## Features

- **Text Extraction**: Extracts raw text and metadata from PDFs using PyMuPDF
- **Entity Recognition**: Identifies named entities (people, organizations, locations) using Stanza NLP
- **Keyword Extraction**: Extracts important keywords using TF-IDF or frequency analysis
- **OCR Processing**: Uses Tesseract OCR to extract text from images within PDFs
- **Summarization**: Generates summaries using transformer models with GPU acceleration
- **Intelligent Chunking**: Divides documents into semantically meaningful chunks
- **GPU Acceleration**: Utilizes CUDA for faster processing when available

## Prerequisites

### Python Environment
1. Python 3.8+ is recommended
2. Install dependencies: `pip install -r requirements.txt`

### External Dependencies
- **Tesseract OCR**: Required for extracting text from images in PDFs
  - Windows: Download and install from https://github.com/UB-Mannheim/tesseract/wiki
  - Make sure to check "Add to PATH" during installation
  - Verify installation with `tesseract --version`

### GPU Support (Optional but Recommended)
- CUDA Toolkit 11.7+ for GPU acceleration
- Compatible NVIDIA GPU

## Installation

1. Clone or download this repository
2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Install Tesseract OCR (see above)
4. Run the installation script (optional):
   ```
   python install_dependencies.py
   ```

## Usage
1. Place your PDF files in a directory
2. Run the script: 
   ```
   python data_enrichment_enhanced_gpu_fixed_v2.py
   ```
3. Follow the prompts to specify input directory and output file
4. The script will process PDFs and generate a JSON file with enriched data

## Output
The script generates a JSON file containing:
- Extracted text and metadata
- Named entities and their categories
- Keywords
- Summaries
- Quality metrics
- OCR text from images (if Tesseract is installed)

## Performance Tips
- Use a GPU for significantly faster processing, especially for summarization
- Process PDFs in small batches to manage memory usage
- For large documents, consider adjusting the batch size parameter

## Troubleshooting
- If you encounter CUDA errors, try reducing the GPU memory fraction in the script
- If Tesseract is not detected, ensure it's properly installed and added to your PATH
- For memory issues, reduce batch sizes or process fewer documents at once
