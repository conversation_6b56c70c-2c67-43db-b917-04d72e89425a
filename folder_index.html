<!DOCTYPE html> 
<html> 
<head> 
<title>Folder Index</title> 
<style> 
body { font-family: Arial, sans-serif; margin: 20px; } 
h1, h2 { color: #333; } 
.stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; } 
pre { background-color: #f9f9f9; padding: 10px; border-radius: 5px; overflow-x: auto; } 
</style> 
</head> 
<body> 
<h1>Folder Index</h1> 
<div class="stats"> 
<h2>Statistics</h2> 
<p>Indexed at: Mon 05/05/2025 21:28:38.28</p> 
</div> 
<h2>Directory Structure</h2> 
<pre> 
C:\Users\<USER>\Documents\DonutLord\create_index.bat
C:\Users\<USER>\Documents\DonutLord\Erichmenttool
C:\Users\<USER>\Documents\DonutLord\folder_index.html
C:\Users\<USER>\Documents\DonutLord\folder_index.json
C:\Users\<USER>\Documents\DonutLord\folder_indexer.py
C:\Users\<USER>\Documents\DonutLord\Index-Folder.ps1
C:\Users\<USER>\Documents\DonutLord\index_folder.bat
C:\Users\<USER>\Documents\DonutLord\SimpleIndex.ps1
C:\Users\<USER>\Documents\DonutLord\simple_index.html
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\data_enrichment_enhanced_gpu_fixed_v2.py
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\install_dependencies.py
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\README.md
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\requirements.txt
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\run_pdf_enrichment.bat
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\run_pdf_enrichment.sh
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\sample_pdfs
C:\Users\<USER>\Documents\DonutLord\Erichmenttool\temp_pdf_enrichment_tool\sample_pdfs\README.txt
</pre> 
</body> 
</html> 
