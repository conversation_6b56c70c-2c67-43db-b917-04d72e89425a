# PowerShell script to index the PDF Enrichment Tool directory
$targetDir = "C:\Users\<USER>\Documents\DonutLord\Erichmenttool\ET\temp_pdf_enrichment_tool"
$outputFile = "C:\Users\<USER>\Documents\DonutLord\pdf_enrichment_index.html"

# Get all files and directories
$items = Get-ChildItem -Path $targetDir -Recurse -Force -ErrorAction SilentlyContinue

# Count statistics
$totalFiles = ($items | Where-Object { -not $_.PSIsContainer }).Count
$totalDirs = ($items | Where-Object { $_.PSIsContainer }).Count
$totalSize = ($items | Where-Object { -not $_.PSIsContainer } | Measure-Object -Property Length -Sum).Sum

# Function to convert bytes to human-readable format
function Format-FileSize {
    param ([long]$Size)
    
    $units = @("B", "KB", "MB", "GB", "TB", "PB")
    $size = [double]$Size
    $unitIndex = 0
    
    while ($size -ge 1024 -and $unitIndex -lt ($units.Length - 1)) {
        $size = $size / 1024
        $unitIndex++
    }
    
    return "{0:N2} {1}" -f $size, $units[$unitIndex]
}

# Create HTML content
$html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Enrichment Tool Index</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        .search { margin-bottom: 20px; }
        #searchInput { padding: 8px; width: 300px; }
        .file-content { background-color: #f9f9f9; padding: 10px; border-radius: 5px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>PDF Enrichment Tool Index</h1>
    
    <div class="search">
        <input type="text" id="searchInput" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: $(Get-Date)</p>
        <p>Root Directory: $targetDir</p>
        <p>Total Files: $totalFiles</p>
        <p>Total Directories: $totalDirs</p>
        <p>Total Size: $(Format-FileSize -Size $totalSize)</p>
    </div>
    
    <h2>Directory Structure</h2>
    <table id="dirTable">
        <tr>
            <th>Directory</th>
            <th>Path</th>
            <th>Last Modified</th>
        </tr>
"@

# Add directories
foreach ($dir in ($items | Where-Object { $_.PSIsContainer } | Sort-Object FullName)) {
    $relativePath = $dir.FullName.Substring($targetDir.Length).TrimStart("\")
    if ($relativePath -eq "") { $relativePath = "." }
    
    $html += @"
        <tr class="dir-row" data-path="$($relativePath.ToLower())">
            <td>$($dir.Name)</td>
            <td>$relativePath</td>
            <td>$($dir.LastWriteTime)</td>
        </tr>
"@
}

$html += @"
    </table>
    
    <h2>Files</h2>
    <table id="fileTable">
        <tr>
            <th>File</th>
            <th>Path</th>
            <th>Size</th>
            <th>Last Modified</th>
            <th>Type</th>
        </tr>
"@

# Add files
foreach ($file in ($items | Where-Object { -not $_.PSIsContainer } | Sort-Object FullName)) {
    $relativePath = $file.FullName.Substring($targetDir.Length).TrimStart("\")
    if ($relativePath -eq "") { $relativePath = "." }
    
    # Get file extension and determine type
    $extension = [System.IO.Path]::GetExtension($file.Name).ToLower()
    $fileType = switch ($extension) {
        ".py"  { "Python Script" }
        ".md"  { "Markdown Document" }
        ".txt" { "Text Document" }
        ".bat" { "Batch Script" }
        ".sh"  { "Shell Script" }
        ".pdf" { "PDF Document" }
        default { "Unknown" }
    }
    
    $html += @"
        <tr class="file-row" data-name="$($file.Name.ToLower())" data-path="$($relativePath.ToLower())">
            <td>$($file.Name)</td>
            <td>$relativePath</td>
            <td>$(Format-FileSize -Size $file.Length)</td>
            <td>$($file.LastWriteTime)</td>
            <td>$fileType</td>
        </tr>
"@
}

# Add file contents for text files
$html += @"
    </table>
    
    <h2>File Contents</h2>
"@

foreach ($file in ($items | Where-Object { -not $_.PSIsContainer } | Sort-Object FullName)) {
    $extension = [System.IO.Path]::GetExtension($file.Name).ToLower()
    $relativePath = $file.FullName.Substring($targetDir.Length).TrimStart("\")
    
    # Only include content for text-based files under 100KB
    $textExtensions = @(".py", ".md", ".txt", ".bat", ".sh", ".json", ".yml", ".yaml", ".html", ".css", ".js")
    if ($textExtensions -contains $extension -and $file.Length -lt 102400) {
        try {
            $content = Get-Content -Path $file.FullName -Raw -ErrorAction Stop
            if ($content) {
                $html += @"
    <div class="file-section" id="file-$($file.Name.Replace(".", "-"))">
        <h3>$($file.Name) <span class="file-path">($relativePath)</span></h3>
        <div class="file-content">$([System.Web.HttpUtility]::HtmlEncode($content))</div>
    </div>
"@
            }
        } catch {
            # Skip files that can't be read
        }
    }
}

# Add search functionality
$html += @"
    
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function() {
            var input, filter, dirTable, fileTable, tr, i, txtValue;
            input = document.getElementById('searchInput');
            filter = input.value.toUpperCase();
            
            // Search directories
            dirTable = document.getElementById('dirTable');
            tr = dirTable.getElementsByTagName('tr');
            
            for (i = 1; i < tr.length; i++) {
                var nameCell = tr[i].getElementsByTagName('td')[0];
                var pathCell = tr[i].getElementsByTagName('td')[1];
                
                if (nameCell && pathCell) {
                    var nameText = nameCell.textContent || nameCell.innerText;
                    var pathText = pathCell.textContent || pathCell.innerText;
                    
                    if (nameText.toUpperCase().indexOf(filter) > -1 || pathText.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
            
            // Search files
            fileTable = document.getElementById('fileTable');
            tr = fileTable.getElementsByTagName('tr');
            
            for (i = 1; i < tr.length; i++) {
                var nameCell = tr[i].getElementsByTagName('td')[0];
                var pathCell = tr[i].getElementsByTagName('td')[1];
                
                if (nameCell && pathCell) {
                    var nameText = nameCell.textContent || nameCell.innerText;
                    var pathText = pathCell.textContent || pathCell.innerText;
                    
                    if (nameText.toUpperCase().indexOf(filter) > -1 || pathText.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
            
            // Search file contents
            var fileSections = document.getElementsByClassName('file-section');
            for (i = 0; i < fileSections.length; i++) {
                var heading = fileSections[i].getElementsByTagName('h3')[0];
                var content = fileSections[i].getElementsByClassName('file-content')[0];
                
                if (heading && content) {
                    var headingText = heading.textContent || heading.innerText;
                    var contentText = content.textContent || content.innerText;
                    
                    if (headingText.toUpperCase().indexOf(filter) > -1 || contentText.toUpperCase().indexOf(filter) > -1) {
                        fileSections[i].style.display = '';
                    } else {
                        fileSections[i].style.display = 'none';
                    }
                }
            }
        });
    </script>
</body>
</html>
"@

# Save HTML file
$html | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Index created successfully: $outputFile"
Write-Host "Opening index in browser..."

# Open the HTML file in the default browser
Invoke-Item $outputFile
