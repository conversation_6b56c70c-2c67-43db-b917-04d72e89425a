@echo off
echo PDF Data Enrichment Tool
echo =======================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo Please install Python 3.8 or higher and try again.
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist requirements.txt (
    echo ERROR: requirements.txt not found.
    pause
    exit /b 1
)

echo Checking dependencies...
python -c "import torch, tqdm, fitz, transformers" >nul 2>&1
if %errorlevel% neq 0 (
    echo Some dependencies are missing. Would you like to install them now? (Y/N)
    set /p install_deps=
    if /i "%install_deps%"=="Y" (
        echo Installing dependencies...
        python install_dependencies.py
    ) else (
        echo Skipping dependency installation.
        echo Please run 'python install_dependencies.py' manually if needed.
    )
)

echo.
echo Running PDF Data Enrichment Tool...
echo.
python data_enrichment_enhanced_gpu_fixed_v2.py
echo.
echo Process completed.
pause
