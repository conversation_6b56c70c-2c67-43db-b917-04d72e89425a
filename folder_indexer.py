import os
import datetime
import json
import argparse
import hashlib
import mimetypes
from pathlib import Path

class FolderIndexer:
    """A tool to create a comprehensive index of a folder structure."""
    
    def __init__(self, root_dir='.', output_file='folder_index.json', 
                 output_html='folder_index.html', exclude_dirs=None, 
                 exclude_extensions=None, max_file_size=10485760):
        """
        Initialize the FolderIndexer.
        
        Args:
            root_dir (str): The root directory to index
            output_file (str): The JSON output file path
            output_html (str): The HTML output file path
            exclude_dirs (list): List of directory names to exclude
            exclude_extensions (list): List of file extensions to exclude
            max_file_size (int): Maximum file size to index content (in bytes)
        """
        self.root_dir = os.path.abspath(root_dir)
        self.output_file = output_file
        self.output_html = output_html
        self.exclude_dirs = exclude_dirs or ['.git', 'node_modules', '__pycache__', 'venv']
        self.exclude_extensions = exclude_extensions or ['.pyc', '.pyo', '.pyd', '.obj', '.exe']
        self.max_file_size = max_file_size
        self.index = {
            'indexed_at': datetime.datetime.now().isoformat(),
            'root_directory': self.root_dir,
            'total_files': 0,
            'total_directories': 0,
            'total_size': 0,
            'directories': {}
        }
    
    def should_exclude(self, path):
        """Check if a path should be excluded from indexing."""
        name = os.path.basename(path)
        
        # Check if it's in excluded directories
        if os.path.isdir(path) and name in self.exclude_dirs:
            return True
            
        # Check if it has excluded extension
        if os.path.isfile(path):
            _, ext = os.path.splitext(name)
            if ext.lower() in self.exclude_extensions:
                return True
                
        return False
    
    def get_file_info(self, file_path):
        """Get metadata for a file."""
        try:
            stat = os.stat(file_path)
            file_size = stat.st_size
            
            # Get file type using mimetypes
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = "application/octet-stream"
                
            # Calculate MD5 hash for smaller files
            file_hash = None
            if file_size <= self.max_file_size:
                try:
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()
                except Exception:
                    pass
            
            return {
                'name': os.path.basename(file_path),
                'path': os.path.relpath(file_path, self.root_dir),
                'size': file_size,
                'size_human': self.human_readable_size(file_size),
                'created': datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'mime_type': mime_type,
                'extension': os.path.splitext(file_path)[1].lower(),
                'md5': file_hash
            }
        except Exception as e:
            return {
                'name': os.path.basename(file_path),
                'path': os.path.relpath(file_path, self.root_dir),
                'error': str(e)
            }
    
    def human_readable_size(self, size, decimal_places=2):
        """Convert bytes to human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB', 'PB']:
            if size < 1024.0 or unit == 'PB':
                break
            size /= 1024.0
        return f"{size:.{decimal_places}f} {unit}"
    
    def index_directory(self, directory=None):
        """Recursively index a directory."""
        if directory is None:
            directory = self.root_dir
            
        dir_path = os.path.relpath(directory, self.root_dir)
        if dir_path == '.':
            dir_path = ''
            
        # Initialize directory entry
        if dir_path not in self.index['directories']:
            self.index['directories'][dir_path] = {
                'name': os.path.basename(directory) or os.path.basename(self.root_dir),
                'path': dir_path,
                'files': [],
                'subdirectories': []
            }
            self.index['total_directories'] += 1
        
        try:
            # List all items in the directory
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                # Skip excluded items
                if self.should_exclude(item_path):
                    continue
                    
                if os.path.isdir(item_path):
                    # Add subdirectory to parent's list
                    rel_path = os.path.relpath(item_path, self.root_dir)
                    self.index['directories'][dir_path]['subdirectories'].append(rel_path)
                    
                    # Recursively index subdirectory
                    self.index_directory(item_path)
                    
                elif os.path.isfile(item_path):
                    # Add file info
                    file_info = self.get_file_info(item_path)
                    self.index['directories'][dir_path]['files'].append(file_info)
                    self.index['total_files'] += 1
                    
                    # Add to total size if size is available
                    if 'size' in file_info:
                        self.index['total_size'] += file_info['size']
        
        except Exception as e:
            print(f"Error indexing {directory}: {e}")
    
    def generate_html(self):
        """Generate an HTML report from the index."""
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Folder Index: {os.path.basename(self.root_dir)}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        .stats {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .directory {{ margin-bottom: 30px; border-left: 2px solid #ddd; padding-left: 15px; }}
        .files {{ margin-left: 20px; }}
        .file {{ margin-bottom: 5px; }}
        .search-container {{ margin-bottom: 20px; }}
        #search {{ padding: 8px; width: 300px; }}
        .hidden {{ display: none; }}
    </style>
</head>
<body>
    <h1>Folder Index: {os.path.basename(self.root_dir)}</h1>
    
    <div class="search-container">
        <input type="text" id="search" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: {self.index['indexed_at']}</p>
        <p>Root Directory: {self.index['root_directory']}</p>
        <p>Total Files: {self.index['total_files']}</p>
        <p>Total Directories: {self.index['total_directories']}</p>
        <p>Total Size: {self.human_readable_size(self.index['total_size'])}</p>
    </div>
    
    <h2>Directory Structure</h2>
"""
        
        # Sort directories by path for a more logical display
        sorted_dirs = sorted(self.index['directories'].items(), key=lambda x: x[0] if x[0] else "")
        
        for dir_path, dir_info in sorted_dirs:
            dir_name = dir_info['name']
            display_path = dir_path if dir_path else "Root"
            
            html += f"""
    <div class="directory" data-path="{dir_path}">
        <h3>{display_path}</h3>
"""
            
            # Add files
            if dir_info['files']:
                html += f"""        <div class="files">
            <h4>Files ({len(dir_info['files'])})</h4>
"""
                
                # Sort files by name
                sorted_files = sorted(dir_info['files'], key=lambda x: x['name'].lower())
                
                for file in sorted_files:
                    size_info = f" - {file.get('size_human', 'Unknown size')}" if 'size_human' in file else ""
                    html += f"""            <div class="file" data-name="{file['name'].lower()}">{file['name']}{size_info}</div>
"""
                
                html += "        </div>\n"
            
            html += "    </div>\n"
        
        # Add search functionality
        html += """
    <script>
        document.getElementById('search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            
            // Search in directories and files
            document.querySelectorAll('.directory').forEach(dir => {
                let dirVisible = false;
                
                // Check if directory path matches
                if (dir.getAttribute('data-path').toLowerCase().includes(searchTerm)) {
                    dirVisible = true;
                }
                
                // Check if any files match
                const files = dir.querySelectorAll('.file');
                files.forEach(file => {
                    const fileName = file.getAttribute('data-name');
                    if (fileName.includes(searchTerm)) {
                        file.classList.remove('hidden');
                        dirVisible = true;
                    } else {
                        file.classList.add('hidden');
                    }
                });
                
                // Show/hide directory based on matches
                if (dirVisible) {
                    dir.classList.remove('hidden');
                } else {
                    dir.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
"""
        
        with open(self.output_html, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"HTML report generated: {self.output_html}")
    
    def run(self):
        """Run the indexing process."""
        print(f"Indexing directory: {self.root_dir}")
        self.index_directory()
        
        # Add human-readable total size
        self.index['total_size_human'] = self.human_readable_size(self.index['total_size'])
        
        # Save JSON index
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(self.index, f, indent=2)
        
        print(f"Index saved to: {self.output_file}")
        
        # Generate HTML report
        self.generate_html()
        
        return self.index

def main():
    parser = argparse.ArgumentParser(description='Index a folder structure')
    parser.add_argument('--dir', '-d', default='.', help='Directory to index (default: current directory)')
    parser.add_argument('--output', '-o', default='folder_index.json', help='Output JSON file (default: folder_index.json)')
    parser.add_argument('--html', default='folder_index.html', help='Output HTML file (default: folder_index.html)')
    parser.add_argument('--exclude-dirs', nargs='+', help='Directories to exclude')
    parser.add_argument('--exclude-exts', nargs='+', help='File extensions to exclude')
    parser.add_argument('--max-size', type=int, default=10485760, help='Maximum file size to calculate hash (default: 10MB)')
    
    args = parser.parse_args()
    
    indexer = FolderIndexer(
        root_dir=args.dir,
        output_file=args.output,
        output_html=args.html,
        exclude_dirs=args.exclude_dirs,
        exclude_extensions=args.exclude_exts,
        max_file_size=args.max_size
    )
    
    indexer.run()

if __name__ == "__main__":
    main()
