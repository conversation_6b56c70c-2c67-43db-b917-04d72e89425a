# PowerShell Folder Indexer
# This script creates a comprehensive index of a folder structure

param (
    [string]$RootDir = ".",
    [string]$OutputFile = "folder_index.json",
    [string]$HtmlOutput = "folder_index.html",
    [string[]]$ExcludeDirs = @(".git", "node_modules", "__pycache__", "venv"),
    [string[]]$ExcludeExtensions = @(".pyc", ".pyo", ".pyd", ".obj", ".exe")
)

# Convert to absolute path
$RootDir = Resolve-Path $RootDir
$OutputFile = Join-Path (Get-Location) $OutputFile
$HtmlOutput = Join-Path (Get-Location) $HtmlOutput

# Initialize index object
$index = @{
    indexed_at = (Get-Date).ToString("o")
    root_directory = $RootDir.Path
    total_files = 0
    total_directories = 0
    total_size = 0
    directories = @{}
}

# Function to convert bytes to human-readable format
function Format-FileSize {
    param ([long]$Size)
    
    $units = @("B", "KB", "MB", "GB", "TB", "PB")
    $size = [double]$Size
    $unitIndex = 0
    
    while ($size -ge 1024 -and $unitIndex -lt ($units.Length - 1)) {
        $size = $size / 1024
        $unitIndex++
    }
    
    return "{0:N2} {1}" -f $size, $units[$unitIndex]
}

# Function to check if a path should be excluded
function Should-Exclude {
    param (
        [string]$Path
    )
    
    $name = Split-Path -Leaf $Path
    
    # Check if it's in excluded directories
    if (Test-Path -Path $Path -PathType Container) {
        if ($ExcludeDirs -contains $name) {
            return $true
        }
    }
    
    # Check if it has excluded extension
    if (Test-Path -Path $Path -PathType Leaf) {
        $extension = [System.IO.Path]::GetExtension($name).ToLower()
        if ($ExcludeExtensions -contains $extension) {
            return $true
        }
    }
    
    return $false
}

# Function to get file information
function Get-FileInfo {
    param (
        [string]$FilePath
    )
    
    try {
        $file = Get-Item -Path $FilePath
        $fileSize = $file.Length
        
        # Get file type
        $extension = [System.IO.Path]::GetExtension($file.Name).ToLower()
        
        # Get relative path
        $relativePath = $FilePath.Substring($RootDir.Path.Length).TrimStart("\")
        if ($relativePath -eq "") {
            $relativePath = "."
        }
        
        return @{
            name = $file.Name
            path = $relativePath
            size = $fileSize
            size_human = Format-FileSize -Size $fileSize
            created = $file.CreationTime.ToString("o")
            modified = $file.LastWriteTime.ToString("o")
            extension = $extension
        }
    }
    catch {
        return @{
            name = Split-Path -Leaf $FilePath
            path = $FilePath.Substring($RootDir.Path.Length).TrimStart("\")
            error = $_.Exception.Message
        }
    }
}

# Function to recursively index a directory
function Index-Directory {
    param (
        [string]$Directory = $RootDir
    )
    
    # Get relative path
    $dirPath = $Directory.Substring($RootDir.Path.Length).TrimStart("\")
    if ($dirPath -eq "") {
        $dirPath = "."
    }
    
    # Initialize directory entry if it doesn't exist
    if (-not $index.directories.ContainsKey($dirPath)) {
        $dirName = Split-Path -Leaf $Directory
        if ($dirPath -eq ".") {
            $dirName = Split-Path -Leaf $RootDir
        }
        
        $index.directories[$dirPath] = @{
            name = $dirName
            path = $dirPath
            files = @()
            subdirectories = @()
        }
        
        $index.total_directories++
    }
    
    try {
        # Get all items in the directory
        $items = Get-ChildItem -Path $Directory -Force -ErrorAction SilentlyContinue
        
        foreach ($item in $items) {
            # Skip excluded items
            if (Should-Exclude -Path $item.FullName) {
                continue
            }
            
            if ($item.PSIsContainer) {
                # It's a directory
                $relPath = $item.FullName.Substring($RootDir.Path.Length).TrimStart("\")
                if ($relPath -eq "") {
                    $relPath = "."
                }
                
                # Add to parent's subdirectories
                $index.directories[$dirPath].subdirectories += $relPath
                
                # Recursively index subdirectory
                Index-Directory -Directory $item.FullName
            }
            else {
                # It's a file
                $fileInfo = Get-FileInfo -FilePath $item.FullName
                $index.directories[$dirPath].files += $fileInfo
                $index.total_files++
                
                # Add to total size if size is available
                if ($fileInfo.ContainsKey("size")) {
                    $index.total_size += $fileInfo.size
                }
            }
        }
    }
    catch {
        Write-Host "Error indexing $Directory`: $_" -ForegroundColor Red
    }
}

# Main execution
Write-Host "Indexing directory: $RootDir" -ForegroundColor Cyan
Index-Directory

# Add human-readable total size
$index.total_size_human = Format-FileSize -Size $index.total_size

# Save JSON index
$index | ConvertTo-Json -Depth 10 | Set-Content -Path $OutputFile -Encoding UTF8
Write-Host "Index saved to: $OutputFile" -ForegroundColor Green

# Generate HTML report
$html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Folder Index: $(Split-Path -Leaf $RootDir)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .directory { margin-bottom: 30px; border-left: 2px solid #ddd; padding-left: 15px; }
        .files { margin-left: 20px; }
        .file { margin-bottom: 5px; }
        .search-container { margin-bottom: 20px; }
        #search { padding: 8px; width: 300px; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>Folder Index: $(Split-Path -Leaf $RootDir)</h1>
    
    <div class="search-container">
        <input type="text" id="search" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: $($index.indexed_at)</p>
        <p>Root Directory: $($index.root_directory)</p>
        <p>Total Files: $($index.total_files)</p>
        <p>Total Directories: $($index.total_directories)</p>
        <p>Total Size: $($index.total_size_human)</p>
    </div>
    
    <h2>Directory Structure</h2>
"@

# Sort directories by path
$sortedDirs = $index.directories.GetEnumerator() | Sort-Object -Property Name

foreach ($dir in $sortedDirs) {
    $dirPath = $dir.Key
    $dirInfo = $dir.Value
    $dirName = $dirInfo.name
    $displayPath = if ($dirPath -eq ".") { "Root" } else { $dirPath }
    
    $html += @"
    
    <div class="directory" data-path="$dirPath">
        <h3>$displayPath</h3>
"@
    
    # Add files
    if ($dirInfo.files.Count -gt 0) {
        $html += @"
        
        <div class="files">
            <h4>Files ($($dirInfo.files.Count))</h4>
"@
        
        # Sort files by name
        $sortedFiles = $dirInfo.files | Sort-Object -Property { $_.name.ToLower() }
        
        foreach ($file in $sortedFiles) {
            $sizeInfo = if ($file.ContainsKey("size_human")) { " - $($file.size_human)" } else { "" }
            $html += @"
            <div class="file" data-name="$($file.name.ToLower())">$($file.name)$sizeInfo</div>
"@
        }
        
        $html += @"
        </div>
"@
    }
    
    $html += @"
    </div>
"@
}

# Add search functionality
$html += @"
    
    <script>
        document.getElementById('search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            
            // Search in directories and files
            document.querySelectorAll('.directory').forEach(dir => {
                let dirVisible = false;
                
                // Check if directory path matches
                if (dir.getAttribute('data-path').toLowerCase().includes(searchTerm)) {
                    dirVisible = true;
                }
                
                // Check if any files match
                const files = dir.querySelectorAll('.file');
                files.forEach(file => {
                    const fileName = file.getAttribute('data-name');
                    if (fileName.includes(searchTerm)) {
                        file.classList.remove('hidden');
                        dirVisible = true;
                    } else {
                        file.classList.add('hidden');
                    }
                });
                
                // Show/hide directory based on matches
                if (dirVisible) {
                    dir.classList.remove('hidden');
                } else {
                    dir.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
"@

$html | Set-Content -Path $HtmlOutput -Encoding UTF8
Write-Host "HTML report generated: $HtmlOutput" -ForegroundColor Green

# Open the HTML report in the default browser
Write-Host "Opening HTML report in browser..." -ForegroundColor Cyan
Invoke-Item $HtmlOutput
