﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Enrichment Tool Index</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1, h2, h3 { color: #333; }
        .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        .search { margin-bottom: 20px; }
        #searchInput { padding: 8px; width: 300px; }
        .file-content { background-color: #f9f9f9; padding: 10px; border-radius: 5px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>PDF Enrichment Tool Index</h1>
    
    <div class="search">
        <input type="text" id="searchInput" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: 05/07/2025 08:50:48</p>
        <p>Root Directory: C:\Users\<USER>\Documents\DonutLord\Erichmenttool\ET\temp_pdf_enrichment_tool</p>
        <p>Total Files: 7</p>
        <p>Total Directories: 1</p>
        <p>Total Size: 74.00 KB</p>
    </div>
    
    <h2>Directory Structure</h2>
    <table id="dirTable">
        <tr>
            <th>Directory</th>
            <th>Path</th>
            <th>Last Modified</th>
        </tr>        <tr class="dir-row" data-path="sample_pdfs">
            <td>sample_pdfs</td>
            <td>sample_pdfs</td>
            <td>05/05/2025 21:31:06</td>
        </tr>    </table>
    
    <h2>Files</h2>
    <table id="fileTable">
        <tr>
            <th>File</th>
            <th>Path</th>
            <th>Size</th>
            <th>Last Modified</th>
            <th>Type</th>
        </tr>        <tr class="file-row" data-name="data_enrichment_enhanced_gpu_fixed_v2.py" data-path="data_enrichment_enhanced_gpu_fixed_v2.py">
            <td>data_enrichment_enhanced_gpu_fixed_v2.py</td>
            <td>data_enrichment_enhanced_gpu_fixed_v2.py</td>
            <td>63.00 KB</td>
            <td>05/05/2025 21:31:06</td>
            <td>Python Script</td>
        </tr>        <tr class="file-row" data-name="install_dependencies.py" data-path="install_dependencies.py">
            <td>install_dependencies.py</td>
            <td>install_dependencies.py</td>
            <td>5.00 KB</td>
            <td>05/05/2025 21:31:06</td>
            <td>Python Script</td>
        </tr>        <tr class="file-row" data-name="readme.md" data-path="readme.md">
            <td>README.md</td>
            <td>README.md</td>
            <td>3.00 KB</td>
            <td>05/05/2025 21:31:06</td>
            <td>Markdown Document</td>
        </tr>        <tr class="file-row" data-name="requirements.txt" data-path="requirements.txt">
            <td>requirements.txt</td>
            <td>requirements.txt</td>
            <td>182.00 B</td>
            <td>05/05/2025 21:31:06</td>
            <td>Text Document</td>
        </tr>        <tr class="file-row" data-name="run_pdf_enrichment.bat" data-path="run_pdf_enrichment.bat">
            <td>run_pdf_enrichment.bat</td>
            <td>run_pdf_enrichment.bat</td>
            <td>1.00 KB</td>
            <td>05/05/2025 21:31:06</td>
            <td>Batch Script</td>
        </tr>        <tr class="file-row" data-name="run_pdf_enrichment.sh" data-path="run_pdf_enrichment.sh">
            <td>run_pdf_enrichment.sh</td>
            <td>run_pdf_enrichment.sh</td>
            <td>1.00 KB</td>
            <td>05/05/2025 21:31:06</td>
            <td>Shell Script</td>
        </tr>        <tr class="file-row" data-name="readme.txt" data-path="sample_pdfs\readme.txt">
            <td>README.txt</td>
            <td>sample_pdfs\README.txt</td>
            <td>843.00 B</td>
            <td>05/05/2025 21:31:06</td>
            <td>Text Document</td>
        </tr>    </table>
    
    <h2>File Contents</h2>    
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function() {
            var input, filter, dirTable, fileTable, tr, i, txtValue;
            input = document.getElementById('searchInput');
            filter = input.value.toUpperCase();
            
            // Search directories
            dirTable = document.getElementById('dirTable');
            tr = dirTable.getElementsByTagName('tr');
            
            for (i = 1; i < tr.length; i++) {
                var nameCell = tr[i].getElementsByTagName('td')[0];
                var pathCell = tr[i].getElementsByTagName('td')[1];
                
                if (nameCell && pathCell) {
                    var nameText = nameCell.textContent || nameCell.innerText;
                    var pathText = pathCell.textContent || pathCell.innerText;
                    
                    if (nameText.toUpperCase().indexOf(filter) > -1 || pathText.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
            
            // Search files
            fileTable = document.getElementById('fileTable');
            tr = fileTable.getElementsByTagName('tr');
            
            for (i = 1; i < tr.length; i++) {
                var nameCell = tr[i].getElementsByTagName('td')[0];
                var pathCell = tr[i].getElementsByTagName('td')[1];
                
                if (nameCell && pathCell) {
                    var nameText = nameCell.textContent || nameCell.innerText;
                    var pathText = pathCell.textContent || pathCell.innerText;
                    
                    if (nameText.toUpperCase().indexOf(filter) > -1 || pathText.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
            
            // Search file contents
            var fileSections = document.getElementsByClassName('file-section');
            for (i = 0; i < fileSections.length; i++) {
                var heading = fileSections[i].getElementsByTagName('h3')[0];
                var content = fileSections[i].getElementsByClassName('file-content')[0];
                
                if (heading && content) {
                    var headingText = heading.textContent || heading.innerText;
                    var contentText = content.textContent || content.innerText;
                    
                    if (headingText.toUpperCase().indexOf(filter) > -1 || contentText.toUpperCase().indexOf(filter) > -1) {
                        fileSections[i].style.display = '';
                    } else {
                        fileSections[i].style.display = 'none';
                    }
                }
            }
        });
    </script>
</body>
</html>
