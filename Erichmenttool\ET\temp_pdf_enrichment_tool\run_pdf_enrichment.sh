#!/bin/bash

echo "PDF Data Enrichment Tool"
echo "======================="
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH."
    echo "Please install Python 3.8 or higher and try again."
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
if (( $(echo "$python_version < 3.8" | bc -l) )); then
    echo "WARNING: Python 3.8 or higher is recommended."
    echo "Current version: $python_version"
fi

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    echo "ERROR: requirements.txt not found."
    exit 1
fi

# Check if dependencies are installed
echo "Checking dependencies..."
if ! python3 -c "import torch, tqdm, fitz, transformers" &> /dev/null; then
    echo "Some dependencies are missing. Would you like to install them now? (y/n)"
    read -r install_deps
    if [[ "$install_deps" =~ ^[Yy]$ ]]; then
        echo "Installing dependencies..."
        python3 install_dependencies.py
    else
        echo "Skipping dependency installation."
        echo "Please run 'python3 install_dependencies.py' manually if needed."
    fi
fi

echo ""
echo "Running PDF Data Enrichment Tool..."
echo ""
python3 data_enrichment_enhanced_gpu_fixed_v2.py
echo ""
echo "Process completed."
