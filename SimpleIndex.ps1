# Simple PowerShell Folder Indexer
$outputFile = "folder_index.html"

# Get all files and directories
$items = Get-ChildItem -Path . -Recurse -Force -ErrorAction SilentlyContinue

# Count statistics
$totalFiles = ($items | Where-Object { -not $_.PSIsContainer }).Count
$totalDirs = ($items | Where-Object { $_.PSIsContainer }).Count
$totalSize = ($items | Where-Object { -not $_.PSIsContainer } | Measure-Object -Property Length -Sum).Sum

# Function to convert bytes to human-readable format
function Format-FileSize {
    param ([long]$Size)
    
    $units = @("B", "KB", "MB", "GB", "TB", "PB")
    $size = [double]$Size
    $unitIndex = 0
    
    while ($size -ge 1024 -and $unitIndex -lt ($units.Length - 1)) {
        $size = $size / 1024
        $unitIndex++
    }
    
    return "{0:N2} {1}" -f $size, $units[$unitIndex]
}

# Create HTML content
$html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Folder Index</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        tr:hover { background-color: #f5f5f5; }
        .search { margin-bottom: 20px; }
        #searchInput { padding: 8px; width: 300px; }
    </style>
</head>
<body>
    <h1>Folder Index</h1>
    
    <div class="search">
        <input type="text" id="searchInput" placeholder="Search files and directories...">
    </div>
    
    <div class="stats">
        <h2>Statistics</h2>
        <p>Indexed at: $(Get-Date)</p>
        <p>Total Files: $totalFiles</p>
        <p>Total Directories: $totalDirs</p>
        <p>Total Size: $(Format-FileSize -Size $totalSize)</p>
    </div>
    
    <table id="fileTable">
        <tr>
            <th>Type</th>
            <th>Name</th>
            <th>Path</th>
            <th>Size</th>
            <th>Last Modified</th>
        </tr>
"@

# Add directories first
foreach ($dir in ($items | Where-Object { $_.PSIsContainer } | Sort-Object FullName)) {
    $relativePath = $dir.FullName.Substring((Get-Location).Path.Length).TrimStart("\")
    if ($relativePath -eq "") { $relativePath = "." }
    
    $html += @"
        <tr>
            <td>Directory</td>
            <td>$($dir.Name)</td>
            <td>$relativePath</td>
            <td>-</td>
            <td>$($dir.LastWriteTime)</td>
        </tr>
"@
}

# Add files
foreach ($file in ($items | Where-Object { -not $_.PSIsContainer } | Sort-Object FullName)) {
    $relativePath = $file.FullName.Substring((Get-Location).Path.Length).TrimStart("\")
    if ($relativePath -eq "") { $relativePath = "." }
    
    $html += @"
        <tr>
            <td>File</td>
            <td>$($file.Name)</td>
            <td>$relativePath</td>
            <td>$(Format-FileSize -Size $file.Length)</td>
            <td>$($file.LastWriteTime)</td>
        </tr>
"@
}

# Add search functionality
$html += @"
    </table>
    
    <script>
        document.getElementById('searchInput').addEventListener('keyup', function() {
            var input, filter, table, tr, td, i, txtValue;
            input = document.getElementById('searchInput');
            filter = input.value.toUpperCase();
            table = document.getElementById('fileTable');
            tr = table.getElementsByTagName('tr');
            
            for (i = 1; i < tr.length; i++) {
                // Check name and path columns
                tdName = tr[i].getElementsByTagName('td')[1];
                tdPath = tr[i].getElementsByTagName('td')[2];
                
                if (tdName && tdPath) {
                    txtName = tdName.textContent || tdName.innerText;
                    txtPath = tdPath.textContent || tdPath.innerText;
                    
                    if (txtName.toUpperCase().indexOf(filter) > -1 || txtPath.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = '';
                    } else {
                        tr[i].style.display = 'none';
                    }
                }
            }
        });
    </script>
</body>
</html>
"@

# Save HTML file
$html | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Index created successfully: $outputFile"
Write-Host "Opening index in browser..."

# Open the HTML file in the default browser
Invoke-Item $outputFile
