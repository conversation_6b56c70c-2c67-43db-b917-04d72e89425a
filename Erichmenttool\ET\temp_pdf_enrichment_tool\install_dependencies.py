#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Installation script for PDF Data Enrichment Tool dependencies.
This script installs the required Python packages and provides instructions
for installing external dependencies like Tesseract OCR.
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("WARNING: This tool is recommended to run with Python 3.8 or higher.")
        print(f"Current Python version: {platform.python_version()}")
        return False
    return True

def install_python_dependencies():
    """Install required Python packages from requirements.txt."""
    requirements_file = os.path.join(os.path.dirname(__file__), "requirements.txt")
    
    if not os.path.exists(requirements_file):
        print(f"ERROR: Requirements file not found at {requirements_file}")
        return False
    
    print("Installing required Python packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        print("Python dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Failed to install dependencies: {e}")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed and accessible."""
    try:
        result = subprocess.run(["tesseract", "--version"], 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE,
                               text=True)
        if result.returncode == 0:
            version = result.stdout.split("\n")[0]
            print(f"Tesseract OCR is installed: {version}")
            return True
        else:
            print("Tesseract OCR is not properly installed or not in PATH.")
            return False
    except FileNotFoundError:
        print("Tesseract OCR is not installed or not in PATH.")
        return False

def check_cuda():
    """Check if CUDA is available through PyTorch."""
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print(f"CUDA is available with {device_count} device(s).")
            print(f"GPU: {device_name}")
            return True
        else:
            print("CUDA is not available. GPU acceleration will be disabled.")
            return False
    except ImportError:
        print("PyTorch is not installed. Cannot check CUDA availability.")
        return False
    except Exception as e:
        print(f"Error checking CUDA: {e}")
        return False

def print_tesseract_instructions():
    """Print instructions for installing Tesseract OCR."""
    system = platform.system()
    
    print("\n=== Tesseract OCR Installation Instructions ===")
    
    if system == "Windows":
        print("1. Download the installer from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Run the installer and make sure to check 'Add to PATH' during installation")
        print("3. Restart your command prompt or terminal after installation")
        print("4. Verify installation with: tesseract --version")
    
    elif system == "Darwin":  # macOS
        print("1. Install using Homebrew: brew install tesseract")
        print("2. Verify installation with: tesseract --version")
    
    elif system == "Linux":
        print("1. Install using your package manager:")
        print("   - Ubuntu/Debian: sudo apt install tesseract-ocr")
        print("   - Fedora: sudo dnf install tesseract")
        print("   - Arch Linux: sudo pacman -S tesseract")
        print("2. Verify installation with: tesseract --version")
    
    else:
        print("Please visit https://github.com/tesseract-ocr/tesseract for installation instructions")
    
    print("===================================================\n")

def main():
    """Main installation function."""
    print("\n=== PDF Data Enrichment Tool - Dependency Installation ===\n")
    
    # Check Python version
    check_python_version()
    
    # Install Python dependencies
    python_deps_installed = install_python_dependencies()
    
    # Check for Tesseract OCR
    tesseract_installed = check_tesseract()
    if not tesseract_installed:
        print_tesseract_instructions()
    
    # Check for CUDA (only if Python dependencies were installed)
    if python_deps_installed:
        cuda_available = check_cuda()
    
    print("\n=== Installation Summary ===")
    print(f"Python dependencies: {'Installed' if python_deps_installed else 'Failed'}")
    print(f"Tesseract OCR: {'Installed' if tesseract_installed else 'Not installed or not in PATH'}")
    if python_deps_installed:
        print(f"CUDA availability: {'Available' if cuda_available else 'Not available'}")
    
    print("\nSetup completed. You can now run the PDF Data Enrichment Tool.")
    print("Command: python data_enrichment_enhanced_gpu_fixed_v2.py")

if __name__ == "__main__":
    main()
